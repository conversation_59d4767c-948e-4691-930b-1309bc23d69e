"""
场站和设备相关数据模型
"""
from sqlalchemy import Column, BigInteger, String, Integer, DateTime, Float, Text
from sqlalchemy.sql import func
from app.database import Base


class PileStation(Base):
    """充电站基本信息表"""
    __tablename__ = "pile_station"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    dept_id = Column(BigInteger, comment="部门ID")
    code = Column(String(20), comment="电站代码")
    name = Column(String(200), comment="电站名称（汉）")
    file_name = Column(String(1000), comment="文件名称，即电站的图片路径，多个文件用,隔开")
    longitude = Column(String(30), comment="经度")
    latitude = Column(String(30), comment="纬度")
    dc_ac_num = Column(Integer, comment="交直流混合桩数量")
    install_date = Column(DateTime, comment="安装日期")
    run_date = Column(DateTime, comment="投运日期")
    city_id = Column(String(100), comment="城市id")
    city_name = Column(String(300), comment="城市名称")
    address = Column(String(200), comment="地址")
    status = Column(Integer, comment="状态 1:计划中 2:建设中 3:已建好未投运 4:已投运 5:关闭下线")
    standard_id = Column(BigInteger, comment="计费方案")
    self_charge = Column(Integer, default=1, comment="自行充电 1：是 0：否")
    appointment_charge = Column(Integer, default=0, comment="是否支持预约充电 1:是 0:否")
    appointment_charge_server_fees = Column(Float(8, 4), comment="呼叫充电单次服务费")
    if_open = Column(Integer, default=1, comment="是否对外开放。0：否；1：是。")
    if_emergency = Column(Integer, default=0, comment="是否应急场站 1:是 0 否")
    del_flag = Column(Integer, default=0, comment="删除标记 1：是 0: 否")
    create_time = Column(DateTime, default=func.current_timestamp(), comment="创建时间")
    update_time = Column(DateTime, default=func.current_timestamp(), 
                        onupdate=func.current_timestamp(), comment="更新时间")

    def __repr__(self):
        return f"<PileStation(id={self.id}, name='{self.name}', city='{self.city_name}')>"


class PileDevice(Base):
    """设备表"""
    __tablename__ = "pile_device"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    dept_id = Column(BigInteger, comment="部门ID")
    station_id = Column(String(64), comment="电站id")
    type = Column(Integer, comment="设备类型 1: 充电车 2：充电枪")
    model_id = Column(BigInteger, comment="设备型号ID")
    code = Column(String(40), comment="设备编码")
    bms_sn = Column(String(255), comment="BMS关联的sn (vin)")
    name = Column(String(200), comment="设备名称")
    parent_id = Column(BigInteger, comment="上级ID")
    ac_or_dc = Column(String(100), comment="交直流类型 字典表ac_or_dc")
    operator_id = Column(String(64), comment="组织机构代码")
    install_date = Column(DateTime, comment="安装日期")
    run_date = Column(DateTime, comment="运行日期")
    status = Column(String(100), default="1", comment="设备状态，字典类型device_status")
    create_time = Column(DateTime, comment="创建时间")
    update_time = Column(DateTime, comment="更新时间")
    del_flag = Column(Integer, comment="删除标记 1：是 0: 否")
    connector_id = Column(String(40), comment="合肥充电设备编码")
    device_type = Column(Integer, comment="国标桩类型 1=G3, 2=G1/2, 3=自研")

    def __repr__(self):
        return f"<PileDevice(id={self.id}, station_id='{self.station_id}', bms_sn='{self.bms_sn}')>"


class PileDeviceModel(Base):
    """设备型号信息表"""
    __tablename__ = "pile_device_model"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    type = Column(String(100), comment="设备类型，字典表定义")
    manufacture = Column(String(200), comment="生产厂家")
    name = Column(String(200), comment="设备型号")
    current = Column(Float(10, 2), comment="额定电流")
    comm_way = Column(String(50), comment="数据通讯协议")
    comm_address = Column(String(50), comment="数据通讯地址")
    voltage = Column(Float(10, 2), comment="额定电压")
    temperature = Column(Float(10, 2), comment="温度")
    contact = Column(String(200), comment="联系方式")
    remark = Column(String(250), comment="备注")
    img = Column(String(1000), comment="图片地址")
    rated_power = Column(Float(10, 2), comment="额定功率")
    device_total_power = Column(Float(5, 2), default=0.00, comment="设备总电量(度)")
    power_mode = Column(String(50), comment="供电模式")
    measure_grade = Column(String(50), comment="计量准确度等级")
    operating_life = Column(String(50), comment="机械操作寿命")
    prote_rated_current = Column(String(50), comment="保护额定动作电流")
    prote_rated_time = Column(String(50), comment="保护额定动作时间")
    avg_between_time = Column(String(50), comment="平均故障间隔时间")
    del_flag = Column(Integer, default=0, comment="删除标记 1：是 0：否")
    create_time = Column(DateTime, comment="创建时间")
    create_by = Column(String(30), comment="创建人")
    update_time = Column(DateTime, comment="更新时间")
    update_by = Column(String(30), comment="更新人")

    def __repr__(self):
        return f"<PileDeviceModel(id={self.id}, name='{self.name}', manufacture='{self.manufacture}')>"
