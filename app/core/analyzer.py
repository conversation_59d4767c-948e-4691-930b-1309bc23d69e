"""
数据分析器模块
负责从原始订单数据中提取和计算各项评分指标
"""
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, case, text
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import logging

from app.models.order import PileOrderSnapshot, AppUser
from app.models.station import PileStation, PileDevice, SysBusLog

logger = logging.getLogger(__name__)


class DataAnalyzer:
    """数据分析器类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_station_raw_data(
        self, 
        station_id: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> Dict[str, Any]:
        """
        获取场站原始数据
        
        Args:
            station_id: 场站ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            包含订单数据、设备数据、用户数据的字典
        """
        try:
            # 获取场站基础信息
            station = self.db.query(PileStation).filter(
                PileStation.id == station_id
            ).first()
            
            if not station:
                raise ValueError(f"场站 {station_id} 不存在")
            
            # 获取设备信息
            devices = self.db.query(PileDevice).filter(
                PileDevice.station_id == station_id
            ).all()
            
            # 获取订单数据
            orders = self.db.query(PileOrderSnapshot).filter(
                and_(
                    PileOrderSnapshot.station_id == station_id,
                    PileOrderSnapshot.create_time >= start_date,
                    PileOrderSnapshot.create_time <= end_date,
                    PileOrderSnapshot.status == 4  # 充电完成
                )
            ).all()
            
            # 获取用户数据
            user_ids = [order.user_id for order in orders if order.user_id]
            users = []
            if user_ids:
                users = self.db.query(AppUser).filter(
                    AppUser.id.in_(user_ids)
                ).all()
            
            return {
                "station": station,
                "devices": devices,
                "orders": orders,
                "users": users,
                "period": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "days": (end_date - start_date).days + 1
                }
            }
            
        except Exception as e:
            logger.error(f"获取场站 {station_id} 原始数据失败: {e}")
            raise
    
    def calculate_operation_metrics(self, raw_data: Dict[str, Any]) -> Dict[str, float]:
        """
        计算运营效率指标

        Args:
            raw_data: 原始数据字典

        Returns:
            运营效率指标字典
        """
        try:
            orders = raw_data["orders"]
            devices = raw_data["devices"]
            period_days = raw_data["period"]["days"]
            start_date = raw_data["period"]["start_date"]
            end_date = raw_data["period"]["end_date"]

            if not orders:
                return {
                    "daily_revenue_rate": 0.0,
                    "device_utilization": 0.0,
                    "revenue_per_device": 0.0,
                    "completion_rate": 0.0
                }

            # 去重订单（基于订单ID）
            unique_orders = {}
            for order in orders:
                if order.id not in unique_orders:
                    unique_orders[order.id] = order
            orders = list(unique_orders.values())

            # 计算总收益
            total_revenue = sum([float(order.total_fees or 0) for order in orders])

            # 计算平均设备数量（考虑设备在统计期间的变化）
            avg_device_count = self._calculate_average_device_count(devices, start_date, end_date)
            if avg_device_count == 0:
                avg_device_count = 1  # 避免除零错误

            # 1. 日均收益率
            daily_revenue_rate = total_revenue / period_days if period_days > 0 else 0

            # 2. 设备利用率 (订单数 / (平均设备数 × 运营天数))
            device_utilization = len(orders) / (avg_device_count * period_days) if period_days > 0 else 0

            # 3. 单设备产出
            revenue_per_device = total_revenue / avg_device_count

            # 4. 充电完成率 - 基于传入的订单数据计算
            completion_rate = self._calculate_completion_rate_from_orders(orders, raw_data["station"].id, start_date, end_date)

            return {
                "daily_revenue_rate": round(daily_revenue_rate, 2),
                "device_utilization": round(device_utilization, 4),
                "revenue_per_device": round(revenue_per_device, 2),
                "completion_rate": round(completion_rate, 2)
            }

        except Exception as e:
            logger.error(f"计算运营效率指标失败: {e}")
            raise
    
    def calculate_service_metrics(self, raw_data: Dict[str, Any]) -> Dict[str, float]:
        """
        计算服务质量指标

        Args:
            raw_data: 原始数据字典

        Returns:
            服务质量指标字典
        """
        try:
            orders = raw_data["orders"]

            if not orders:
                return {
                    "avg_charge_time": 0.0,
                    "avg_charge_power": 0.0,
                    "user_retention_rate": 0.0,
                    "failure_rate": 0.0
                }

            # 去重订单
            unique_orders = {}
            for order in orders:
                if order.id not in unique_orders:
                    unique_orders[order.id] = order
            orders = list(unique_orders.values())

            # 1. 平均充电时长 (秒)
            charge_times = [float(order.timelong or 0) for order in orders if order.timelong]
            avg_charge_time = sum(charge_times) / len(charge_times) if charge_times else 0

            # 2. 平均充电量 (kWh)
            charge_powers = [float(order.charge_power or 0) for order in orders if order.charge_power]
            avg_charge_power = sum(charge_powers) / len(charge_powers) if charge_powers else 0

            # 3. 个人用户复购率 (只计算pay_type==1的个人用户)
            user_retention_rate = self._calculate_personal_user_retention(orders)

            # 4. 故障率 (根据normal_end字段计算)
            normal_orders = [order for order in orders if order.normal_end == 1]
            failure_rate = ((len(orders) - len(normal_orders)) / len(orders) * 100) if orders else 0

            return {
                "avg_charge_time": round(avg_charge_time, 2),
                "avg_charge_power": round(avg_charge_power, 4),
                "user_retention_rate": round(user_retention_rate, 2),
                "failure_rate": round(failure_rate, 2)
            }

        except Exception as e:
            logger.error(f"计算服务质量指标失败: {e}")
            raise
    
    def calculate_stability_metrics(self, raw_data: Dict[str, Any]) -> Dict[str, float]:
        """
        计算稳定性指标
        
        Args:
            raw_data: 原始数据字典
            
        Returns:
            稳定性指标字典
        """
        try:
            orders = raw_data["orders"]
            period_days = raw_data["period"]["days"]
            
            if not orders:
                return {
                    "order_growth_trend": 0.0,
                    "revenue_stability": 0.0,
                    "operation_continuity": 0.0,
                    "device_health": 100.0,
                    "time_balance": 0.0
                }
            
            # 1. 订单增长趋势 (需要历史数据对比，这里简化处理)
            order_growth_trend = 0.0  # 暂时设为0，需要历史数据
            
            # 2. 收益稳定性 (基于日收益的变异系数)
            daily_revenues = self._calculate_daily_revenues(orders)
            revenue_stability = self._calculate_stability_score(daily_revenues)
            
            # 3. 运营连续性 (有订单的天数 / 总天数)
            order_days = len(set([order.create_time.date() for order in orders]))
            operation_continuity = (order_days / period_days * 100) if period_days > 0 else 0
            
            # 4. 设备健康度 (假设所有设备都正常)
            device_health = 100.0
            
            # 5. 时段均衡性 (基于不同时段订单分布的均衡程度)
            time_balance = self._calculate_time_balance(orders)
            
            return {
                "order_growth_trend": round(order_growth_trend, 2),
                "revenue_stability": round(revenue_stability, 2),
                "operation_continuity": round(operation_continuity, 2),
                "device_health": round(device_health, 2),
                "time_balance": round(time_balance, 2)
            }
            
        except Exception as e:
            logger.error(f"计算稳定性指标失败: {e}")
            raise
    
    def _calculate_daily_revenues(self, orders: List) -> List[float]:
        """计算每日收益"""
        daily_revenues = {}
        for order in orders:
            date = order.create_time.date()
            if date not in daily_revenues:
                daily_revenues[date] = 0
            daily_revenues[date] += float(order.total_fees or 0)
        
        return list(daily_revenues.values())
    
    def _calculate_stability_score(self, values: List[float]) -> float:
        """计算稳定性评分 (1 - 变异系数)"""
        if not values or len(values) < 2:
            return 100.0
        
        mean_val = np.mean(values)
        if mean_val == 0:
            return 100.0
        
        std_val = np.std(values)
        cv = std_val / mean_val  # 变异系数
        stability = max(0, (1 - cv) * 100)
        
        return stability
    
    def _calculate_time_balance(self, orders: List) -> float:
        """计算时段均衡性"""
        if not orders:
            return 0.0
        
        # 按小时统计订单分布
        hour_counts = {}
        for order in orders:
            hour = order.create_time.hour
            hour_counts[hour] = hour_counts.get(hour, 0) + 1
        
        # 计算各时段订单数的标准差
        counts = list(hour_counts.values())
        if len(counts) < 2:
            return 100.0
        
        mean_count = np.mean(counts)
        std_count = np.std(counts)
        
        if mean_count == 0:
            return 100.0
        
        # 均衡性 = 1 - (标准差/均值)
        balance = max(0, (1 - std_count / mean_count) * 100)

        return balance

    def _calculate_average_device_count(self, devices: List, start_date: datetime, end_date: datetime) -> float:
        """
        计算统计期间的平均设备数量
        基于设备调运记录表(sys_bus_log)计算更准确的设备数量
        """
        try:
            if not devices:
                return 0.0

            # 获取场站ID（从第一个设备获取）
            station_id = devices[0].station_id if devices else None
            if not station_id:
                return len(devices)

            # 查询设备调运记录
            device_logs = self.db.query(SysBusLog).filter(
                and_(
                    SysBusLog.type.in_(['设备调入', '设备调出', '设备安装', '设备移除']),
                    SysBusLog.bus_unique == station_id,
                    SysBusLog.create_time >= start_date,
                    SysBusLog.create_time <= end_date
                )
            ).order_by(SysBusLog.create_time).all()

            # 如果没有调运记录，使用当前设备数量
            if not device_logs:
                return len(devices)

            # 计算每日设备数量
            period_days = (end_date - start_date).days + 1
            daily_device_counts = []
            current_device_count = len(devices)  # 从当前设备数量开始

            # 按日期计算设备数量变化
            current_date = start_date
            log_index = 0

            while current_date <= end_date:
                # 检查当天是否有设备调运记录
                while (log_index < len(device_logs) and
                       device_logs[log_index].create_time.date() == current_date.date()):
                    log = device_logs[log_index]

                    # 根据操作类型调整设备数量
                    if log.type in ['设备调入', '设备安装']:
                        current_device_count += 1
                    elif log.type in ['设备调出', '设备移除']:
                        current_device_count = max(0, current_device_count - 1)

                    log_index += 1

                daily_device_counts.append(current_device_count)
                current_date += timedelta(days=1)

            # 计算平均设备数量
            avg_device_count = sum(daily_device_counts) / len(daily_device_counts) if daily_device_counts else len(devices)

            return max(1, avg_device_count)  # 至少为1，避免除零

        except Exception as e:
            logger.error(f"计算平均设备数量失败: {e}")
            # 如果计算失败，返回当前设备数量
            return len(devices) if devices else 1

    def _calculate_completion_rate_from_orders(self, completed_orders: List, station_id: str, start_date: datetime, end_date: datetime) -> float:
        """
        基于已有的完成订单数据计算充电完成率
        优化：避免重复查询数据库，直接使用传入的订单数据
        """
        try:
            # 查询所有状态的订单（包括未完成的）
            all_orders = self.db.query(PileOrderSnapshot).filter(
                and_(
                    PileOrderSnapshot.station_id == station_id,
                    PileOrderSnapshot.create_time >= start_date,
                    PileOrderSnapshot.create_time <= end_date
                )
            ).all()

            if not all_orders:
                return 0.0  # 没有订单时返回0%

            # 去重订单
            unique_orders = {}
            for order in all_orders:
                if order.id not in unique_orders:
                    unique_orders[order.id] = order
            all_orders = list(unique_orders.values())

            # 计算完成订单数（status==4 且 timelong>0）
            completed_count = len([
                order for order in all_orders
                if order.status == 4 and (order.timelong or 0) > 0
            ])

            completion_rate = (completed_count / len(all_orders)) * 100

            return round(completion_rate, 2)

        except Exception as e:
            logger.error(f"计算充电完成率失败: {e}")
            return 100.0  # 出错时返回默认值

    def _calculate_completion_rate(self, station_id: str, start_date: datetime, end_date: datetime) -> float:
        """
        计算充电完成率（保留原方法作为备用）
        查询所有状态的订单，计算完成率
        """
        try:
            # 查询所有状态的订单
            all_orders = self.db.query(PileOrderSnapshot).filter(
                and_(
                    PileOrderSnapshot.station_id == station_id,
                    PileOrderSnapshot.create_time >= start_date,
                    PileOrderSnapshot.create_time <= end_date
                )
            ).all()

            if not all_orders:
                return 0.0  # 没有订单时返回0%

            # 去重订单
            unique_orders = {}
            for order in all_orders:
                if order.id not in unique_orders:
                    unique_orders[order.id] = order
            all_orders = list(unique_orders.values())

            # 计算完成订单数（status==4 且 timelong>0）
            completed_orders = [
                order for order in all_orders
                if order.status == 4 and (order.timelong or 0) > 0
            ]

            completion_rate = (len(completed_orders) / len(all_orders)) * 100

            return round(completion_rate, 2)

        except Exception as e:
            logger.error(f"计算充电完成率失败: {e}")
            return 100.0  # 出错时返回默认值

    def _calculate_personal_user_retention(self, orders: List) -> float:
        """
        计算个人用户复购率
        只计算pay_type==1的个人用户
        """
        try:
            # 筛选个人用户订单 (pay_type==1)
            personal_orders = [order for order in orders if getattr(order, 'pay_type', None) == 1]

            if not personal_orders:
                return 0.0

            # 统计个人用户的订单次数
            user_order_counts = {}
            for order in personal_orders:
                if order.person_user_id:
                    user_order_counts[order.person_user_id] = user_order_counts.get(order.person_user_id, 0) + 1

            if not user_order_counts:
                return 0.0

            # 计算复购用户数（订单数>1的用户）
            repeat_users = sum(1 for count in user_order_counts.values() if count > 1)
            total_users = len(user_order_counts)

            retention_rate = (repeat_users / total_users * 100) if total_users > 0 else 0

            return retention_rate

        except Exception as e:
            logger.error(f"计算个人用户复购率失败: {e}")
            return 0.0
