# 如何运行测试

## 1. 环境准备

### 1.1 安装依赖
```bash
pip install -r requirements.txt
```

### 1.2 配置环境变量
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑.env文件，配置真实的数据库连接
vim .env
```

**重要配置项**:
```bash
# 读库：正式数据库连接
READ_DATABASE_URL=mysql+pymysql://username:password@host:port/chargepile-v3.0

# 写库：测试数据库连接  
WRITE_DATABASE_URL=mysql+pymysql://username:password@host:port/chargepile-test

# 开启SQL日志（调试用）
DATABASE_ECHO=True
```

## 2. 运行测试的几种方式

### 2.1 使用pytest运行（推荐）
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_database.py

# 运行特定测试函数
pytest tests/test_station_data.py::test_read_stations

# 显示详细输出
pytest -v -s

# 运行测试并显示覆盖率
pytest --cov=app
```

### 2.2 直接运行Python文件
```bash
# 运行场站数据测试
python tests/test_station_data.py

# 运行数据库连接测试
python -m pytest tests/test_database.py -v
```

### 2.3 在IDE中运行
- **VSCode**: 点击测试函数旁边的运行按钮
- **PyCharm**: 右键测试函数选择"Run"

## 3. 测试场站数据连接

### 3.1 快速测试数据库连接
```bash
# 运行场站数据测试，查看数据库中的实际数据
python tests/test_station_data.py
```

**预期输出**:
```
开始测试数据库连接和数据读取...
找到 5 个已投运场站:
- ID: 123, 名称: 北京朝阳充电站, 城市: 北京市, 设备数: 8
- ID: 124, 名称: 上海浦东充电站, 城市: 上海市, 设备数: 6
...
✓ 场站数据读取测试通过
```

### 3.2 检查数据质量
测试会自动检查：
- 场站数据完整性
- 订单数据质量
- 场站和订单的关联关系
- 数据字段的有效性

## 4. 常见问题和解决方案

### 4.1 数据库连接失败
**错误**: `pymysql.err.OperationalError: (2003, "Can't connect to MySQL server")`

**解决方案**:
1. 检查数据库服务是否启动
2. 确认.env文件中的数据库连接信息正确
3. 检查网络连接和防火墙设置

### 4.2 表不存在错误
**错误**: `pymysql.err.ProgrammingError: (1146, "Table 'xxx' doesn't exist")`

**解决方案**:
1. 确认读库中有原始数据表 (pile_station, pile_order_snapshot等)
2. 运行数据库初始化创建评分表:
```python
from app.database import init_database
init_database()
```

### 4.3 权限问题
**错误**: `pymysql.err.OperationalError: (1045, "Access denied")`

**解决方案**:
1. 检查数据库用户名和密码
2. 确认用户有相应数据库的访问权限
3. 读库只需要SELECT权限，写库需要CREATE、INSERT、UPDATE权限

## 5. 测试数据库设置

### 5.1 创建测试数据库
```sql
-- 创建测试数据库
CREATE DATABASE IF NOT EXISTS `chargepile-test` 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建测试用户（可选）
CREATE USER 'test_user'@'%' IDENTIFIED BY 'test_password';
GRANT ALL PRIVILEGES ON `chargepile-test`.* TO 'test_user'@'%';
FLUSH PRIVILEGES;
```

### 5.2 初始化评分表
```python
# 在Python中运行
from app.database import init_database
init_database()
```

或者直接运行应用，会自动初始化：
```bash
python -m app.main
```

## 6. 调试技巧

### 6.1 开启SQL日志
在.env文件中设置：
```bash
DATABASE_ECHO=True
```
这样可以看到所有执行的SQL语句

### 6.2 使用断点调试
```python
# 在测试代码中添加断点
import pdb; pdb.set_trace()

# 或者使用IDE的断点功能
```

### 6.3 查看详细错误信息
```bash
# 运行测试时显示详细信息
pytest -v -s --tb=long
```

## 7. 持续集成测试

### 7.1 GitHub Actions配置示例
```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:5.7
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: chargepile-test
        ports:
          - 3306:3306
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run tests
        run: pytest
```

## 8. 性能测试

### 8.1 测试数据库查询性能
```python
import time
from app.database import get_read_db

def test_query_performance():
    start_time = time.time()
    with next(get_read_db()) as db:
        # 执行查询
        result = db.query(PileStation).limit(1000).all()
    end_time = time.time()
    print(f"查询耗时: {end_time - start_time:.2f}秒")
```

### 8.2 并发测试
```python
import concurrent.futures
import threading

def test_concurrent_queries():
    def query_data():
        with next(get_read_db()) as db:
            return db.query(PileStation).count()
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(query_data) for _ in range(10)]
        results = [future.result() for future in futures]
    
    print(f"并发查询结果: {results}")
```

现在您可以运行测试来验证数据库连接和数据读取功能了！
