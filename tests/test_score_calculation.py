"""
评分计算算法单元测试
"""
import pytest
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock

from app.core.analyzer import DataAnalyzer
from app.core.calculator import ScoreCalculator, ScoreGrade


class TestDataAnalyzer:
    """数据分析器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.db_mock = Mock()
        self.analyzer = DataAnalyzer(self.db_mock)
    
    def test_calculate_operation_metrics_empty_orders(self):
        """测试空订单情况下的运营指标计算"""
        raw_data = {
            "orders": [],
            "devices": [Mock(), Mock()],  # 2个设备
            "period": {"days": 30}
        }
        
        result = self.analyzer.calculate_operation_metrics(raw_data)
        
        assert result["daily_revenue_rate"] == 0.0
        assert result["device_utilization"] == 0.0
        assert result["revenue_per_device"] == 0.0
        assert result["completion_rate"] == 0.0
    
    def test_calculate_operation_metrics_normal_case(self):
        """测试正常情况下的运营指标计算"""
        # 模拟订单数据
        orders = []
        for i in range(100):  # 100个订单
            order = Mock()
            order.total_fees = 50.0  # 每单50元
            orders.append(order)
        
        devices = [Mock(), Mock()]  # 2个设备
        
        raw_data = {
            "orders": orders,
            "devices": devices,
            "period": {"days": 30}
        }
        
        result = self.analyzer.calculate_operation_metrics(raw_data)
        
        # 验证计算结果 (使用近似比较避免浮点精度问题)
        assert abs(result["daily_revenue_rate"] - (5000.0 / 30)) < 0.01  # 总收益/天数
        assert abs(result["device_utilization"] - (100 / (2 * 30))) < 0.01  # 订单数/(设备数*天数)
        assert abs(result["revenue_per_device"] - (5000.0 / 2)) < 0.01  # 总收益/设备数
        assert result["completion_rate"] == 100.0  # 完成率
    
    def test_calculate_service_metrics(self):
        """测试服务质量指标计算"""
        # 模拟订单数据
        orders = []
        user_ids = []
        for i in range(50):
            order = Mock()
            order.timelong = 2.5  # 2.5小时
            order.charge_power = 45.0  # 45kWh
            order.user_id = f"user_{i % 20}"  # 20个不同用户，有重复
            order.normal_end = 1  # 正常结束
            orders.append(order)
            user_ids.append(order.user_id)
        
        raw_data = {
            "orders": orders,
            "users": [],
            "period": {"days": 30}
        }
        
        result = self.analyzer.calculate_service_metrics(raw_data)
        
        # 验证计算结果
        assert result["avg_charge_time"] == 2.5
        assert result["avg_charge_power"] == 45.0
        assert result["failure_rate"] == 0.0  # 所有订单都正常结束
        
        # 验证用户复购率计算
        unique_users = len(set(user_ids))
        repeat_users = len(user_ids) - unique_users
        expected_retention = (repeat_users / unique_users * 100) if unique_users > 0 else 0
        assert result["user_retention_rate"] == expected_retention
    
    def test_calculate_stability_metrics(self):
        """测试稳定性指标计算"""
        # 模拟订单数据 - 分布在不同日期和时间
        orders = []
        base_date = datetime(2025, 1, 1)
        
        for i in range(60):
            order = Mock()
            order.total_fees = 50.0 + (i % 10) * 5  # 收益有一定波动
            order.create_time = base_date + timedelta(days=i % 30, hours=i % 24)
            orders.append(order)
        
        raw_data = {
            "orders": orders,
            "period": {"days": 30}
        }
        
        result = self.analyzer.calculate_stability_metrics(raw_data)
        
        # 验证基本指标存在
        assert "revenue_stability" in result
        assert "operation_continuity" in result
        assert "time_balance" in result
        assert result["device_health"] == 100.0  # 默认值
        
        # 运营连续性应该大于0（有订单的天数/总天数）
        assert result["operation_continuity"] > 0


class TestScoreCalculator:
    """评分计算器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.calculator = ScoreCalculator()
    
    def test_percentile_normalize_higher_is_better(self):
        """测试分位数标准化 - 数值越高越好"""
        values = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
        
        normalized = self.calculator.percentile_normalize(values, higher_is_better=True)
        
        # 验证标准化结果
        assert len(normalized) == len(values)
        assert all(0 <= score <= 100 for score in normalized)
        assert normalized[-1] >= normalized[0]  # 最高值应该得到最高分
    
    def test_percentile_normalize_lower_is_better(self):
        """测试分位数标准化 - 数值越低越好"""
        values = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        
        normalized = self.calculator.percentile_normalize(values, higher_is_better=False)
        
        # 验证标准化结果
        assert len(normalized) == len(values)
        assert all(0 <= score <= 100 for score in normalized)
        assert normalized[0] >= normalized[-1]  # 最低值应该得到最高分
    
    def test_calculate_dimension_score(self):
        """测试维度评分计算"""
        metrics = {
            "metric1": 80.0,
            "metric2": 90.0,
            "metric3": 70.0
        }
        
        weights = {
            "metric1": 0.5,
            "metric2": 0.3,
            "metric3": 0.2
        }
        
        score = self.calculator.calculate_dimension_score(metrics, weights)
        
        # 验证加权平均计算
        expected = 80.0 * 0.5 + 90.0 * 0.3 + 70.0 * 0.2
        assert score == expected
    
    def test_calculate_total_score(self):
        """测试综合评分计算"""
        operation_metrics = {
            "daily_revenue_rate": 1000.0,
            "device_utilization": 0.8,
            "revenue_per_device": 2000.0,
            "completion_rate": 95.0
        }
        
        service_metrics = {
            "avg_charge_time": 2.5,
            "avg_charge_power": 45.0,
            "user_retention_rate": 60.0,
            "failure_rate": 5.0
        }
        
        stability_metrics = {
            "order_growth_trend": 10.0,
            "revenue_stability": 85.0,
            "operation_continuity": 90.0,
            "device_health": 100.0,
            "time_balance": 75.0
        }
        
        result = self.calculator.calculate_total_score(
            operation_metrics,
            service_metrics,
            stability_metrics
        )
        
        # 验证返回结构
        assert "total_score" in result
        assert "grade" in result
        assert "dimension_scores" in result
        assert "detailed_metrics" in result
        
        # 验证维度评分
        dimension_scores = result["dimension_scores"]
        assert "operation_score" in dimension_scores
        assert "service_score" in dimension_scores
        assert "stability_score" in dimension_scores
        
        # 验证总分范围
        assert 0 <= result["total_score"] <= 100
    
    def test_get_score_grade(self):
        """测试评分等级判定"""
        assert self.calculator.get_score_grade(95) == ScoreGrade.S
        assert self.calculator.get_score_grade(85) == ScoreGrade.A
        assert self.calculator.get_score_grade(75) == ScoreGrade.B
        assert self.calculator.get_score_grade(65) == ScoreGrade.C
        assert self.calculator.get_score_grade(55) == ScoreGrade.D
        
        # 边界值测试
        assert self.calculator.get_score_grade(90) == ScoreGrade.S
        assert self.calculator.get_score_grade(89.9) == ScoreGrade.A
        assert self.calculator.get_score_grade(80) == ScoreGrade.A
        assert self.calculator.get_score_grade(79.9) == ScoreGrade.B
    
    def test_batch_calculate_scores(self):
        """测试批量评分计算"""
        stations_metrics = []
        
        # 创建3个场站的测试数据
        for i in range(3):
            station_data = {
                "station_id": f"station_{i}",
                "operation_metrics": {
                    "daily_revenue_rate": 1000 + i * 100,
                    "device_utilization": 0.7 + i * 0.1,
                    "revenue_per_device": 2000 + i * 200,
                    "completion_rate": 90 + i * 2
                },
                "service_metrics": {
                    "avg_charge_time": 2.0 + i * 0.5,
                    "avg_charge_power": 40 + i * 5,
                    "user_retention_rate": 50 + i * 10,
                    "failure_rate": 5 - i
                },
                "stability_metrics": {
                    "order_growth_trend": i * 5,
                    "revenue_stability": 80 + i * 5,
                    "operation_continuity": 85 + i * 5,
                    "device_health": 100,
                    "time_balance": 70 + i * 10
                }
            }
            stations_metrics.append(station_data)
        
        results = self.calculator.batch_calculate_scores(stations_metrics)
        
        # 验证结果
        assert len(results) == 3
        
        for i, result in enumerate(results):
            assert result["station_id"] == f"station_{i}"
            assert "total_score" in result
            assert "grade" in result
            assert 0 <= result["total_score"] <= 100


class TestScoreCalculationIntegration:
    """评分计算集成测试"""
    
    def test_full_calculation_workflow(self):
        """测试完整的评分计算流程"""
        # 这里可以添加端到端的集成测试
        # 模拟从数据获取到评分计算的完整流程
        pass


if __name__ == "__main__":
    pytest.main([__file__])
